/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { IStyleData, Nullable } from '@univerjs/core';
import type { IDocumentSkeletonFontStyle } from '@univerjs/engine-render';
import { FontCache, getFontStyleString } from '@univerjs/engine-render';

export const PADDING_H = 6;
export const PADDING_V = 2;
export const MARGIN_H = 4;
export const MARGIN_V = 4;
export const CELL_PADDING_H = 6;
export const CELL_PADDING_V = 6;
export const ICON_PLACE = 14;

export function measureDropdownItemText(text: string, style: Nullable<IStyleData>) {
    const fontStyle = getFontStyleString(style ?? undefined);
    const bBox = FontCache.getTextSize(text, fontStyle);
    return bBox;
}

export function getDropdownItemSize(text: string, fontStyle: IDocumentSkeletonFontStyle) {
    const bBox = FontCache.getTextSize(text, fontStyle);
    const rectWidth = bBox.width + PADDING_H * 2;

    const { ba, bd } = bBox;
    const height = ba + bd;
    return {
        width: rectWidth,
        height: height + (PADDING_V * 2),
        ba,
    };
}

export interface IDropdownLayoutInfo {
    layout: {
        width: number;
        height: number;
        ba: number;
    };
    text: string;
}

export interface IDropdownLine {
    width: number;
    height: number;
    items: (IDropdownLayoutInfo & {
        left: number;
        clipped?: boolean;
        clippedWidth?: number;
    })[];
}

export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFontStyle, cellWidth: number, cellHeight: number) {
    const cellPaddingH = ICON_PLACE + CELL_PADDING_H * 2;
    const widthAvailableForContent = cellWidth - cellPaddingH;
    const heightAvailableForContent = cellHeight - CELL_PADDING_V * 2;
    const textLayout = items.map((item) => ({
        layout: getDropdownItemSize(item, fontStyle),
        text: item,
    }));

    // 修改：所有选项都放在一行，不换行
    const singleLine: IDropdownLine = {
        width: 0,
        height: 0,
        items: [],
    };

    let currentLeft = 0;
    textLayout.forEach((item, index) => {
        const { layout } = item;
        const { width, height } = layout;

        // 检查是否有足够空间显示完整选项
        if (currentLeft + width <= widthAvailableForContent) {
            // 完整显示选项
            singleLine.items.push({
                ...item,
                left: currentLeft,
            });
            singleLine.width = currentLeft + width;
            singleLine.height = Math.max(singleLine.height, height);

            // 如果不是最后一个选项，添加间距
            if (index < textLayout.length - 1) {
                currentLeft += width + MARGIN_H;
            } else {
                currentLeft += width;
            }
        } else if (currentLeft < widthAvailableForContent) {
            // 部分显示选项（能显示多少就显示多少）
            const remainingWidth = widthAvailableForContent - currentLeft;
            if (remainingWidth > 0) {
                singleLine.items.push({
                    ...item,
                    left: currentLeft,
                    // 添加一个标记表示这个选项被截断了
                    clipped: true,
                    clippedWidth: remainingWidth,
                });
                singleLine.width = widthAvailableForContent;
                singleLine.height = Math.max(singleLine.height, height);
            }
            // 后续选项都无法显示，直接跳出循环
            return;
        }
        // 完全超出宽度的选项直接忽略
    });

    const lines = [singleLine];
    const totalHeight = singleLine.height;
    const maxLineWidth = singleLine.width;

    return {
        lines,
        totalHeight,
        contentWidth: widthAvailableForContent,
        contentHeight: heightAvailableForContent,
        cellAutoHeight: totalHeight + CELL_PADDING_V * 2,
        calcAutoWidth: maxLineWidth + cellPaddingH,
    };
}
